"""
Binance WebSocket client for real-time data streaming
"""

import websocket
import json
import threading
import time, os
from datetime import datetime
from typing import Dict, Callable, Optional, List

import logging

from .config import *

logger = logging.getLogger(__name__)

class BinanceWebSocketClient:
    """WebSocket client for Binance real-time data"""

    def __init__(self, on_message_callback: Callable[[Dict], None]):
        self.on_message_callback = on_message_callback
        self.ws = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # seconds

    def connect(self, symbol: str = "btcusdt"):
        """Connect to Binance WebSocket stream"""
        try:
            # Convert symbol to lowercase for Binance API
            symbol_lower = symbol.lower()

            # Construct WebSocket URL for 1-minute klines
            url = f"{WEBSOCKET_URL}{symbol_lower}@kline_1m"

            logger.info(f"Connecting to Binance WebSocket: {url}")

            # Create WebSocket connection
            self.ws = websocket.WebSocketApp(
                url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            # Start WebSocket in a separate thread
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()

            return True

        except Exception as e:
            logger.error(f"Error connecting to Binance WebSocket: {e}")
            return False

    def disconnect(self):
        """Disconnect from WebSocket"""
        try:
            if self.ws:
                self.is_connected = False
                self.ws.close()
                logger.info("Disconnected from Binance WebSocket")
        except Exception as e:
            logger.error(f"Error disconnecting from WebSocket: {e}")

    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.is_connected = True
        self.reconnect_attempts = 0
        logger.info("Binance WebSocket connection opened")

    def _on_message(self, ws, message):
        """Handle incoming WebSocket message"""
        try:
            data = json.loads(message)

            # Check if this is a kline (candlestick) message
            if 'k' in data:
                kline_data = data['k']

                # Only process closed candles
                if kline_data['x']:  # x indicates if the kline is closed
                    candle = self._parse_kline_data(kline_data)

                    # Call the callback function
                    if self.on_message_callback:
                        self.on_message_callback(candle)

        except Exception as e:
            logger.error(f"Error processing WebSocket message: {e}")

    def _on_error(self, ws, error):
        """Handle WebSocket error"""
        logger.error(f"Binance WebSocket error: {error}")
        self.is_connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        self.is_connected = False
        logger.warning(f"Binance WebSocket closed: {close_status_code} - {close_msg}")

        # Attempt to reconnect
        if self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"Attempting to reconnect ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
            time.sleep(self.reconnect_delay)
            self._reconnect()
        else:
            logger.error("Max reconnection attempts reached. Giving up.")

    def _reconnect(self):
        """Attempt to reconnect"""
        try:
            if self.ws:
                self.ws.run_forever()
        except Exception as e:
            logger.error(f"Error during reconnection: {e}")

    def _parse_kline_data(self, kline_data: Dict) -> Dict:
        """Parse kline data into standardized format"""
        try:
            # Convert timestamp to datetime
            open_time = datetime.fromtimestamp(kline_data['t'] / 1000)
            close_time = datetime.fromtimestamp(kline_data['T'] / 1000)

            candle = {
                'Open time': open_time,
                'Close time': close_time,
                'Open': float(kline_data['o']),
                'High': float(kline_data['h']),
                'Low': float(kline_data['l']),
                'Close': float(kline_data['c']),
                'Volume': float(kline_data['v']),
                'Quote asset volume': float(kline_data['q']),
                'Number of trades': int(kline_data['n']),
                'Taker buy base asset volume': float(kline_data['V']),
                'Taker buy quote asset volume': float(kline_data['Q']),
                'symbol': kline_data['s'],
                'interval': kline_data['i']
            }

            return candle

        except Exception as e:
            logger.error(f"Error parsing kline data: {e}")
            return {}

    def is_alive(self) -> bool:
        """Check if WebSocket connection is alive"""
        return self.is_connected and self.ws_thread.is_alive()

class BinanceDataFetcher:
    """Fetch historical data from Binance REST API"""

    def __init__(self):
        self.base_url = BINANCE_BASE_URL

    def fetch_historical_klines(self, symbol: str, interval: str = "1m",
                               limit: int = 1000) -> Optional[List[Dict]]:
        """
        Fetch historical kline data from Binance

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            interval: Kline interval (1m, 5m, 15m, 1h, 1d)
            limit: Number of klines to fetch (max 1000)

        Returns:
            List of kline dictionaries or None if error
        """
        try:
            import requests

            url = f"{self.base_url}/api/v3/klines"
            params = {
                'symbol': symbol.upper(),
                'interval': interval,
                'limit': limit
            }

            response = requests.get(url, params=params)
            response.raise_for_status()

            data = response.json()

            # Convert to standardized format
            klines = []
            for kline in data:
                candle = {
                    'Open time': datetime.fromtimestamp(kline[0] / 1000),
                    'Open': float(kline[1]),
                    'High': float(kline[2]),
                    'Low': float(kline[3]),
                    'Close': float(kline[4]),
                    'Volume': float(kline[5]),
                    'Close time': datetime.fromtimestamp(kline[6] / 1000),
                    'Quote asset volume': float(kline[7]),
                    'Number of trades': int(kline[8]),
                    'Taker buy base asset volume': float(kline[9]),
                    'Taker buy quote asset volume': float(kline[10]),
                }
                klines.append(candle)

            logger.info(f"Fetched {len(klines)} historical klines for {symbol}")
            return klines

        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return None

    def save_to_csv(self, klines: List[Dict], filename: str):
        """Save klines data to CSV file"""
        try:
            import pandas as pd

            df = pd.DataFrame(klines)
            df.set_index('Open time', inplace=True)

            # Create data directory if it doesn't exist
            os.makedirs(DATA_DIR, exist_ok=True)

            filepath = os.path.join(DATA_DIR, filename)
            df.to_csv(filepath)

            logger.info(f"Saved {len(klines)} klines to {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error saving to CSV: {e}")
            return None


def create_binance_hook(data_manager, strategy_engine, chart_generator, trade_engine=None):
    """
    Create a hook function for processing Binance data

    Args:
        data_manager: DataManager instance
        strategy_engine: StrategyEngine instance
        chart_generator: ChartGenerator instance
        trade_engine: Optional TradeEngine instance for live trading

    Returns:
        Callable hook function
    """

    def binance_data_hook(candle_data: Dict):
        """
        Process incoming candle data from Binance

        Args:
            candle_data: Candle data from WebSocket
        """
        try:
            symbol = candle_data.get('symbol', 'BTCUSDT')

            # Convert symbol format (BTCUSDT -> BTCUSD)
            if symbol.endswith('USDT'):
                symbol = symbol[:-1]  # Remove 'T' from 'USDT'

            # Add new candle to data manager
            success = data_manager.add_new_candle(symbol, 1, candle_data)
            if not success:
                logger.error(f"Failed to add candle for {symbol}")
                return

            # Update higher timeframes incrementally
            df_1m = data_manager.get_dataframe(symbol, 1)
            if df_1m is not None and len(df_1m) > 0:
                latest_1m_candle = df_1m.iloc[-1]
                latest_1m_time = df_1m.index[-1]

                for timeframe in [5, 15, 60, 1440]:
                    if timeframe in TIMEFRAMES:
                        data_manager.update_higher_timeframe_candle(
                            symbol, timeframe, latest_1m_candle, latest_1m_time
                        )

            # Update all analysis data for all timeframes
            for timeframe in TIMEFRAMES:
                df = data_manager.get_dataframe(symbol, timeframe)
                if df is not None and len(df) > 0:
                    latest_timestamp = df.index[-1]
                    data_manager.update_data_to_new_timestamp(symbol, latest_timestamp, timeframe)

            # Process through TradeEngine if available, otherwise use legacy analysis
            if trade_engine and trade_engine.trade_mode != "live":
                return
            # Use TradeEngine for unified processing
            candle_result = trade_engine.process_candle(
                symbol=symbol,
                candle_data=candle_data
            )

            # Log current status
            latest_candle = data_manager.get_latest_candle(symbol, 1)
            if latest_candle is not None:
                logger.debug(f"Processed candle for {symbol}: Close={latest_candle['Close']}")

        except Exception as e:
            logger.error(f"Error in Binance data hook: {e}")

    return binance_data_hook

import hmac
import hashlib
import requests
from urllib.parse import urlencode

class BinanceFuturesClient:
    """Binance Futures API client for trading operations"""

    def __init__(self, api_key: str, api_secret: str, testnet: bool = False):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://testnet.binancefuture.com" if testnet else "https://fapi.binance.com"
        self.headers = {
            'X-MBX-APIKEY': api_key
        }

    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    def _make_request(self, method: str, endpoint: str, params: dict = None, signed: bool = False) -> Optional[dict]:
        """Make authenticated request to Binance API"""
        try:
            url = f"{self.base_url}{endpoint}"

            if params is None:
                params = {}

            if signed:
                params['timestamp'] = int(time.time() * 1000)
                query_string = urlencode(params)
                params['signature'] = self._generate_signature(query_string)

            if method == 'GET':
                response = requests.get(url, params=params, headers=self.headers)
            elif method == 'POST':
                response = requests.post(url, params=params, headers=self.headers)
            elif method == 'DELETE':
                response = requests.delete(url, params=params, headers=self.headers)

            response.raise_for_status()
            return response.json()

        except Exception as e:
            logger.error(f"API request error: {e}")
            return None

    def get_account_info(self) -> Optional[Dict]:
        """Get futures account information"""
        return self._make_request('GET', '/fapi/v2/account', signed=True)

    def get_balance(self) -> Optional[List[Dict]]:
        """Get futures account balance"""
        account_info = self.get_account_info()
        if account_info:
            return account_info.get('assets', [])
        return None

    def get_usdt_balance(self) -> Optional[float]:
        """Get USDT balance specifically"""
        balances = self.get_balance()
        if balances:
            for balance in balances:
                if balance['asset'] == 'USDT':
                    return float(balance['walletBalance'])
        return None

    def open_position(self, symbol: str, side: str, quantity: float,
                     order_type: str = "MARKET", price: float = None,
                     time_in_force: str = "GTC") -> Optional[Dict]:
        """
        Open futures position

        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            side: 'BUY' or 'SELL'
            quantity: Position size
            order_type: 'MARKET' or 'LIMIT'
            price: Price for limit orders
            time_in_force: 'GTC', 'IOC', 'FOK'
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': order_type.upper(),
            'quantity': quantity
        }

        if order_type.upper() == 'LIMIT':
            if price is None:
                logger.error("Price required for limit orders")
                return None
            params['price'] = price
            params['timeInForce'] = time_in_force

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def set_stop_loss(self, symbol: str, side: str, quantity: float,
                     stop_price: float, price: float = None) -> Optional[Dict]:
        """
        Set stop loss order

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL' (opposite of position)
            quantity: Position size to close
            stop_price: Stop trigger price
            price: Limit price (optional, uses STOP_MARKET if None)
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': 'STOP_MARKET' if price is None else 'STOP',
            'quantity': quantity,
            'stopPrice': stop_price,
            'timeInForce': 'GTC'
        }

        if price is not None:
            params['price'] = price
            params['type'] = 'STOP'

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def set_take_profit(self, symbol: str, side: str, quantity: float,
                       price: float, time_in_force: str = "GTC") -> Optional[Dict]:
        """
        Set take profit limit order

        Args:
            symbol: Trading symbol
            side: 'BUY' or 'SELL' (opposite of position)
            quantity: Position size to close
            price: Take profit price
            time_in_force: Order time in force
        """
        params = {
            'symbol': symbol.upper(),
            'side': side.upper(),
            'type': 'LIMIT',
            'quantity': quantity,
            'price': price,
            'timeInForce': time_in_force
        }

        return self._make_request('POST', '/fapi/v1/order', params, signed=True)

    def get_open_positions(self) -> Optional[List[Dict]]:
        """Get all open positions"""
        account_info = self.get_account_info()
        if account_info:
            positions = account_info.get('positions', [])
            return [pos for pos in positions if float(pos['positionAmt']) != 0]
        return None

    def get_open_orders(self, symbol: str = None) -> Optional[List[Dict]]:
        """Get open orders"""
        params = {}
        if symbol:
            params['symbol'] = symbol.upper()

        return self._make_request('GET', '/fapi/v1/openOrders', params, signed=True)

    def cancel_order(self, symbol: str, order_id: int) -> Optional[Dict]:
        """Cancel an order"""
        params = {
            'symbol': symbol.upper(),
            'orderId': order_id
        }

        return self._make_request('DELETE', '/fapi/v1/order', params, signed=True)

    def close_position(self, symbol: str, quantity: float = None) -> Optional[Dict]:
        """
        Close position using market order

        Args:
            symbol: Trading symbol
            quantity: Quantity to close (None for full position)
        """
        if quantity is None:
            # Get current position size
            positions = self.get_open_positions()
            if not positions:
                return None

            position = next((p for p in positions if p['symbol'] == symbol.upper()), None)
            if not position:
                return None

            quantity = abs(float(position['positionAmt']))
            side = 'SELL' if float(position['positionAmt']) > 0 else 'BUY'
        else:
            # Determine side based on current position
            positions = self.get_open_positions()
            position = next((p for p in positions if p['symbol'] == symbol.upper()), None)
            if not position:
                return None

            side = 'SELL' if float(position['positionAmt']) > 0 else 'BUY'

        return self.open_position(symbol, side, quantity, "MARKET")
